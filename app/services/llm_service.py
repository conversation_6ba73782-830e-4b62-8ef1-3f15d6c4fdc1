import openai
from typing import List, Dict
from app.core.config import settings
from app.core.utils import fast_response
from app.models.chat import ChatMessage
import uuid
import json


class LLMService:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.api_base_url  # 如果使用自定义端点
        )
        # 简单的内存存储对话历史
        self.conversations: Dict[str, List[ChatMessage]] = {}

    async def chat(self, message: str, conversation_id: str = None) -> tuple[str, str]:
        """
        发送消息给LLM并返回响应
        返回: (响应消息, 对话ID)
        """
        if not conversation_id:
            conversation_id = str(uuid.uuid4())

        # 获取或创建对话历史
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = []

        # 添加用户消息到历史
        user_message = ChatMessage(role="user", content=message)
        self.conversations[conversation_id].append(user_message)

        # 准备发送给API的消息
        messages = []
        for msg in self.conversations[conversation_id]:
            messages.append({"role": msg.role, "content": msg.content})

        try:
            # 调用LLM API
            response = self.client.chat.completions.create(
                model=settings.model_name,
                messages=messages,
                max_tokens=settings.max_tokens,
                temperature=settings.temperature
            )

            # 提取响应内容
            assistant_content = response.choices[0].message.content

            # 添加助手响应到历史
            assistant_message = ChatMessage(role="assistant", content=assistant_content)
            self.conversations[conversation_id].append(assistant_message)

            return assistant_content, conversation_id

        except Exception as e:
            return f"抱歉，处理您的请求时出现错误: {str(e)}", conversation_id

    def get_conversation_history(self, conversation_id: str) -> List[ChatMessage]:
        """获取对话历史"""
        return self.conversations.get(conversation_id, [])

    def clear_conversation(self, conversation_id: str) -> bool:
        """清除对话历史"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False