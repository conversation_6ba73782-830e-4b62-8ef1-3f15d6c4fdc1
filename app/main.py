from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.routers import chat

app = FastAPI(
    title="AI助手 API",
    description="一个简单的AI助手服务",
    version="1.0.0"
)

# 添加CORS中间件以支持前端调用
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(chat.router, prefix="/api/v1", tags=["chat"])

@app.get("/")
async def root():
    return {"message": "AI助手服务正在运行"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}