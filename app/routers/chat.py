from fastapi import APIRouter, HTTPException

from app.core.utils import fast_response
from app.models.chat import ChatRequest, ChatResponse
from app.services.llm_service import LLMService
from datetime import datetime

router = APIRouter()
llm_service = LLMService()
print(llm_service)
@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口"""
    # try:
    response_message, conversation_id = await llm_service.chat(
        message=request.message,
        conversation_id=request.conversation_id
    )

    return ChatResponse(
        message=response_message,
        conversation_id=conversation_id,
        timestamp=datetime.now()
    )
# except Exception as e:
#     raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversation/{conversation_id}")
async def get_conversation(conversation_id: str):
    """获取对话历史"""
    history = llm_service.get_conversation_history(conversation_id)
    return {"conversation_id": conversation_id, "messages": history}


@router.delete("/conversation/{conversation_id}")
async def clear_conversation(conversation_id: str):
    """清除对话历史"""
    success = llm_service.clear_conversation(conversation_id)
    if success:
        return {"message": "对话历史已清除"}
    else:
        raise HTTPException(status_code=404, detail="对话不存在")